<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="EasyCodeTableSetting">
    <option name="tableInfoMap">
      <map>
        <entry key="gesb.public.display_screen">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="显示屏ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="id" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="关联生产企业信息" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="manufacturerInfo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="所属镇街" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="townStreet" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="显示屏尺寸（长*宽）" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="screenSizeSqm" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="业主单位名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="ownerUnitName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="业主单位法人姓名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="legalRepresentativeName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="法人电话号码" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="legalRepresentativePhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="管理员姓名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="adminName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="管理员电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="adminPhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="经度" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="longitude" />
                    <option name="type" value="java.lang.Double" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="纬度" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="latitude" />
                    <option name="type" value="java.lang.Double" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="项目承建公司名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="projectContractorName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="项目承建公司联系人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="projectContractorContact" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="项目承建公司联系电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="projectContractorPhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="运营公司" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="operatingCompany" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="运营公司联系人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="operatingCompanyContact" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="运营公司联系电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="operatingCompanyPhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="品牌及型号" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="brandAndModel" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="生产销售厂商名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="manufacturerName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="生产销售厂商电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="manufacturerPhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="网络连接方式" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="networkConnectionMethod" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="联网IP地址" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="networkIpAddress" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否开放wifi无线网络" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="wifiNetwork" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="有无APP投屏功能" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="appProjection" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否有大屏内容的管理后台" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="managementDashboard" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="管理后台的网络IP地址或域名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="ipDomain" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否落实用户实名、日志留存、违法信息屏蔽等安全技术措施" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="securityMeasures" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="管理后台开发厂商名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="dashboardDeveloperName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否已送达风险告知书" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="riskNotification" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否签订承诺书" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="commitment" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否已采取防范措施" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="measures" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="属地分局治安大队核查人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="localTeamInspector" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="属地派出所" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="policeStation" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="属地派出所核查人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="policeInspector" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="属地分局网警中队核查人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="teamInspector" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否删除" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isDeleted" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="creator" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="修改人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updator" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="修改时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="备注" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="remarks" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="DisplayScreen" />
              <option name="preName" value="" />
              <option name="saveModelName" value="screen" />
              <option name="savePackageName" value="com.fiberhome.zw.screen" />
              <option name="savePath" value="./src/main/java/com/fiberhome/zw/screen" />
              <option name="templateGroupName" value="Default" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="gesb.public.enterprise_info">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="企业ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="id" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="name" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="详细地址" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="address" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="负责人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="responsiblePerson" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="负责人电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="responsiblePersonPhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="类型" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="type" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否提供销售清单" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="providesSalesList" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="产品" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="products" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="官网" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="officialWebsite" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="开发厂商名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="developerName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="开发厂商归属地" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="developerLocation" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="开发厂商联系人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="developerContactPerson" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="开发厂商联系人电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="developerContactPhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="设备生产商名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="deviceManufacturerName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="设备生产商归属地" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="deviceManufacturerLocation" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="设备生产商联系人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="deviceManufacturerContactPerson" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="设备生产商联系人电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="deviceManufacturerContactPhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否有广告发布系统" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="hasAdvertisementSystem" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="管理后台的网络IP地址或域名" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="dashboardIpOrDomain" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否存在安全漏洞" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="hasSecurityVulnerability" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否遭攻击" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="hasBeenAttacked" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="漏洞是否落实整改" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="vulnerabilityRemedyImplementation" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="管理后台开发厂商名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="dashboardDeveloperName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="管理后台开发厂商联系人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="dashboardDeveloperContactPerson" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="管理后台开发厂商联系人电话" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="dashboardDeveloperContactPhone" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否已送达风险告知书" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="notification" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否签订承诺书" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="commitment" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="属地分局治安大队核查人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="teamInspector" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="属地派出所" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="localPoliceStation" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="属地派出所核查人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="localPoliceInspector" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="属地网警中队核查人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="localCyberPoliceTeamInspector" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="是否删除" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="isDeleted" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="creator" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="修改人" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updator" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createdTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="修改时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updatedTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="备注" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="remarks" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="EnterpriseInfo" />
              <option name="preName" value="" />
              <option name="saveModelName" value="" />
              <option name="savePackageName" value="" />
              <option name="savePath" value="" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="iao.public.qyzhzs_07">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="aicid" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="apprdate" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="copynum" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="country" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="countryCn" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="district" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="dom" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="domdistrict" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="empnum" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="entitytype" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="entname" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="enttype" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="enttypeCn" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="estdate" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="industryco" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="industryphy" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="localadm" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="name" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="opfrom" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="opscope" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="opscotyp" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="opscotypeCn" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="opto" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="orgform" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="pripid" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="reccap" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="reccapusd" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="regcap" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="regcapcur" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="regcapcurCn" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="regcapusd" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="regno" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="regorg" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="regorgCn" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="regstate" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="regstateCn" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="reporttype" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="sExtDatatime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="sExtFromnode" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="tel" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="town" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="uniscid" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="lxsbid" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="topicAddTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="topicUpdateTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="allMd5Key" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="md5HashKey" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="operatetype" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="rowNum" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="基础能力得分" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="创新能力得分" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="成长能力得分" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="信用评分" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="影响能力得分" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="综合指数得分" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="zcdssqxzwm" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="zcdsszjzwm" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="zcdsscszwm" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="Qyzhzs07" />
              <option name="preName" value="" />
              <option name="saveModelName" value="boss" />
              <option name="savePackageName" value="com.fiberhome.gesbebss.police" />
              <option name="savePath" value="./src/main/java/com/fiberhome/gesbebss/police" />
              <option name="templateGroupName" value="Default" />
            </TableInfoDTO>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>