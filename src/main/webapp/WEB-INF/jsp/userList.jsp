<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线用户列表 - 用户跟踪系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
        }
        
        .user-info {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .user-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .info-item {
            text-align: center;
        }
        
        .info-item .label {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .info-item .value {
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }
        
        .current-user {
            color: #1976d2;
        }
        
        .online-count {
            color: #28a745;
        }
        
        .content {
            padding: 30px;
        }
        
        .section-title {
            color: #333;
            font-size: 20px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #42a5f5;
        }
        
        .user-list {
            display: grid;
            gap: 15px;
        }
        
        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #42a5f5;
            transition: transform 0.2s;
        }
        
        .user-item:hover {
            transform: translateX(5px);
        }
        
        .user-item.current {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left-color: #1976d2;
        }
        
        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .login-time {
            font-size: 12px;
            color: #666;
        }
        
        .user-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-badge {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .current-badge {
            background: #1976d2;
        }
        
        .force-logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
        }
        
        .force-logout-btn:hover {
            background: #c82333;
        }
        
        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #42a5f5;
            color: white;
        }

        .btn-primary:hover {
            background: #1976d2;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .empty-message {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            display: none;
        }
        
        .message.success {
            background: #28a745;
        }
        
        .message.error {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>在线用户管理</h1>
            <p>实时监控用户状态</p>
        </div>
        
        <div class="user-info">
            <div class="user-info-grid">
                <div class="info-item">
                    <div class="label">当前用户</div>
                    <div class="value current-user">${currentUser}</div>
                </div>
                <div class="info-item">
                    <div class="label">在线用户数</div>
                    <div class="value online-count">${userCount} 人</div>
                </div>
                <div class="info-item">
                    <div class="label">系统状态</div>
                    <div class="value" style="color: #28a745;">正常运行</div>
                </div>
            </div>
        </div>
        
        <div class="content">
            <h2 class="section-title">在线用户列表</h2>
            
            <c:choose>
                <c:when test="${empty onlineUsers}">
                    <div class="empty-message">
                        暂无在线用户
                    </div>
                </c:when>
                <c:otherwise>
                    <div class="user-list">
                        <c:forEach var="user" items="${onlineUsers}">
                            <div class="user-item ${user eq currentUser ? 'current' : ''}">
                                <div class="user-info">
                                    <div class="user-name">${user}</div>
                                    <div class="login-time">登录时间: ${loginTimes[user]}</div>
                                </div>
                                <div class="user-status">
                                    <span class="status-badge ${user eq currentUser ? 'current-badge' : ''}">
                                        ${user eq currentUser ? '当前用户' : '在线'}
                                    </span>
                                    <c:if test="${user ne currentUser}">
                                        <button class="force-logout-btn" onclick="forceLogout('${user}')">
                                            强制下线
                                        </button>
                                    </c:if>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </c:otherwise>
            </c:choose>
            
            <div class="actions">
                <button class="btn btn-primary" onclick="refreshPage()">刷新列表</button>
                <form action="/logout" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger">退出登录</button>
                </form>
            </div>
        </div>
    </div>
    
    <div id="message" class="message"></div>
    
    <script>
        function refreshPage() {
            window.location.reload();
        }
        
        function forceLogout(username) {
            if (confirm('确定要强制用户 "' + username + '" 下线吗？')) {
                fetch('/forceLogout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=' + encodeURIComponent(username)
                })
                .then(response => response.text())
                .then(data => {
                    if (data.startsWith('success:')) {
                        showMessage(data.substring(8), 'success');
                        setTimeout(refreshPage, 1500);
                    } else if (data.startsWith('error:')) {
                        showMessage(data.substring(6), 'error');
                    }
                })
                .catch(error => {
                    showMessage('操作失败：' + error.message, 'error');
                });
            }
        }
        
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = 'message ' + type;
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 3000);
        }
        
        // 自动刷新页面（每30秒）
        setInterval(refreshPage, 30000);
    </script>
</body>
</html>
