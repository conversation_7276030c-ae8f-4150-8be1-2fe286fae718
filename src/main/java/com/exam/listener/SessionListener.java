package com.exam.listener;

import javax.servlet.annotation.WebListener;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;
import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

@WebListener
public class SessionListener implements HttpSessionListener {
    
    // 存储所有在线用户的Session
    private static final Map<String, HttpSession> onlineUsers = new ConcurrentHashMap<>();
    // 存储用户登录时间
    private static final Map<String, String> loginTimes = new ConcurrentHashMap<>();
    
    @Override
    public void sessionCreated(HttpSessionEvent se) {
        HttpSession session = se.getSession();
        System.out.println("Session创建: " + session.getId());
    }
    
    @Override
    public void sessionDestroyed(HttpSessionEvent se) {
        HttpSession session = se.getSession();
        String username = (String) session.getAttribute("username");
        
        if (username != null) {
            onlineUsers.remove(username);
            loginTimes.remove(username);
            System.out.println("用户下线: " + username + ", Session: " + session.getId());
        }
    }
    
    // 用户登录时调用
    public static void userLogin(String username, HttpSession session) {
        onlineUsers.put(username, session);
        session.setAttribute("username", username);

        // 记录登录时间
        String loginTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        loginTimes.put(username, loginTime);
        session.setAttribute("loginTime", loginTime);

        System.out.println("用户上线: " + username + ", Session: " + session.getId() + ", 登录时间: " + loginTime);
    }
    
    // 用户登出时调用
    public static void userLogout(String username) {
        HttpSession session = onlineUsers.remove(username);
        loginTimes.remove(username);
        if (session != null) {
            try {
                session.invalidate();
                System.out.println("用户登出: " + username);
            } catch (IllegalStateException e) {
                // Session已经失效
            }
        }
    }
    
    // 强制用户下线
    public static boolean forceLogout(String username) {
        HttpSession session = onlineUsers.get(username);
        if (session != null) {
            try {
                session.invalidate();
                onlineUsers.remove(username);
                loginTimes.remove(username);
                System.out.println("强制下线用户: " + username);
                return true;
            } catch (IllegalStateException e) {
                // Session已经失效
                onlineUsers.remove(username);
                loginTimes.remove(username);
                return false;
            }
        }
        return false;
    }
    
    // 获取所有在线用户
    public static Map<String, HttpSession> getOnlineUsers() {
        return new ConcurrentHashMap<>(onlineUsers);
    }
    
    // 获取在线用户数量
    public static int getOnlineUserCount() {
        return onlineUsers.size();
    }
    
    // 检查用户是否在线
    public static boolean isUserOnline(String username) {
        return onlineUsers.containsKey(username);
    }

    // 获取用户登录时间
    public static String getUserLoginTime(String username) {
        return loginTimes.get(username);
    }

    // 获取所有用户登录时间
    public static Map<String, String> getAllLoginTimes() {
        return new ConcurrentHashMap<>(loginTimes);
    }
}
