package com.exam.controller;

import com.exam.listener.SessionListener;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.Map;

@Controller
public class UserController {
    
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }
    
    @GetMapping("/login")
    public String loginPage(HttpSession session) {
        // 如果用户已经登录，直接跳转到用户列表
        String username = (String) session.getAttribute("username");
        if (username != null && SessionListener.isUserOnline(username)) {
            return "redirect:/userList";
        }
        return "login";
    }
    
    @PostMapping("/login")
    public String login(@RequestParam String username, HttpSession session, Model model) {
        if (username == null || username.trim().isEmpty()) {
            model.addAttribute("error", "用户名不能为空");
            return "login";
        }

        // 清理当前Session中可能存在的旧用户信息
        String currentUser = (String) session.getAttribute("username");
        if (currentUser != null) {
            SessionListener.userLogout(currentUser);
        }

        // 检查用户是否已经在线
        if (SessionListener.isUserOnline(username)) {
            model.addAttribute("error", "用户已在线，请先下线或联系管理员");
            return "login";
        }

        // 用户登录
        SessionListener.userLogin(username, session);
        return "redirect:/userList";
    }
    
    @GetMapping("/userList")
    public String userList(HttpSession session, Model model) {
        String username = (String) session.getAttribute("username");
        if (username == null) {
            return "redirect:/login";
        }
        
        Map<String, HttpSession> onlineUsers = SessionListener.getOnlineUsers();
        Map<String, String> loginTimes = SessionListener.getAllLoginTimes();

        model.addAttribute("currentUser", username);
        model.addAttribute("onlineUsers", onlineUsers.keySet());
        model.addAttribute("loginTimes", loginTimes);
        model.addAttribute("userCount", SessionListener.getOnlineUserCount());
        
        return "userList";
    }
    
    @PostMapping("/logout")
    public String logout(HttpSession session) {
        String username = (String) session.getAttribute("username");
        if (username != null) {
            SessionListener.userLogout(username);
        }
        return "redirect:/login";
    }
    
    @PostMapping("/forceLogout")
    @ResponseBody
    public String forceLogout(@RequestParam String username, HttpSession session) {
        String currentUser = (String) session.getAttribute("username");
        if (currentUser == null) {
            return "error:未登录";
        }
        
        if (currentUser.equals(username)) {
            return "error:不能强制下线自己";
        }
        
        boolean success = SessionListener.forceLogout(username);
        if (success) {
            return "success:用户 " + username + " 已被强制下线";
        } else {
            return "error:用户不在线或下线失败";
        }
    }
}
