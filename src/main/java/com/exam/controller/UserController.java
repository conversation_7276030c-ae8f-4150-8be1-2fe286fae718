package com.exam.controller;

import com.exam.listener.SessionListener;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.Map;

@Controller
public class UserController {
    
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }
    
    @GetMapping("/login")
    public String loginPage(HttpSession session) {
        // 检查当前会话是否有用户信息
        String username = (String) session.getAttribute("username");
        if (username != null) {
            // 验证用户是否真的在线，并且会话ID匹配
            if (SessionListener.isUserOnline(username)) {
                HttpSession userSession = SessionListener.getOnlineUsers().get(username);
                // 确保是同一个会话
                if (userSession != null && userSession.getId().equals(session.getId())) {
                    return "redirect:/userList";
                } else {
                    // 会话不匹配，清理当前会话的用户信息
                    session.removeAttribute("username");
                    session.removeAttribute("loginTime");
                }
            } else {
                // 用户不在线，清理会话信息
                session.removeAttribute("username");
                session.removeAttribute("loginTime");
            }
        }
        return "login";
    }
    
    @PostMapping("/login")
    public String login(@RequestParam String username, HttpSession session, Model model) {
        if (username == null || username.trim().isEmpty()) {
            model.addAttribute("error", "用户名不能为空");
            return "login";
        }

        // 清理当前Session中可能存在的旧用户信息
        String currentUser = (String) session.getAttribute("username");
        if (currentUser != null) {
            SessionListener.userLogout(currentUser);
        }

        // 检查用户是否已经在线
        if (SessionListener.isUserOnline(username)) {
            model.addAttribute("error", "用户已在线，请先下线或联系管理员");
            return "login";
        }

        // 用户登录
        SessionListener.userLogin(username, session);
        return "redirect:/userList";
    }
    
    @GetMapping("/userList")
    public String userList(HttpSession session, Model model) {
        String username = (String) session.getAttribute("username");
        if (username == null) {
            return "redirect:/login";
        }

        // 验证用户是否真的在线，并且会话ID匹配
        if (!SessionListener.isUserOnline(username)) {
            // 用户不在线，清理会话并重定向到登录页
            session.removeAttribute("username");
            session.removeAttribute("loginTime");
            return "redirect:/login";
        }

        HttpSession userSession = SessionListener.getOnlineUsers().get(username);
        if (userSession == null || !userSession.getId().equals(session.getId())) {
            // 会话不匹配，清理当前会话并重定向到登录页
            session.removeAttribute("username");
            session.removeAttribute("loginTime");
            return "redirect:/login";
        }

        Map<String, HttpSession> onlineUsers = SessionListener.getOnlineUsers();
        Map<String, String> loginTimes = SessionListener.getAllLoginTimes();

        model.addAttribute("currentUser", username);
        model.addAttribute("onlineUsers", onlineUsers.keySet());
        model.addAttribute("loginTimes", loginTimes);
        model.addAttribute("userCount", SessionListener.getOnlineUserCount());

        return "userList";
    }
    
    @PostMapping("/logout")
    public String logout(HttpSession session) {
        String username = (String) session.getAttribute("username");
        if (username != null) {
            SessionListener.userLogout(username);
        }
        return "redirect:/login";
    }
    
    @PostMapping("/forceLogout")
    @ResponseBody
    public String forceLogout(@RequestParam String username, HttpSession session) {
        String currentUser = (String) session.getAttribute("username");
        if (currentUser == null) {
            return "error:未登录";
        }
        
        if (currentUser.equals(username)) {
            return "error:不能强制下线自己";
        }
        
        boolean success = SessionListener.forceLogout(username);
        if (success) {
            return "success:用户 " + username + " 已被强制下线";
        } else {
            return "error:用户不在线或下线失败";
        }
    }
}
